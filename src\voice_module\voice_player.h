#ifndef VOICE_PLAYER_H
#define VOICE_PLAYER_H

#include <Arduino.h>
#include <Audio.h>
#include <stdint.h>

#ifdef __cplusplus
extern "C"
{
#endif

    // Voice player status
    typedef enum
    {
        VOICE_PLAYER_IDLE = 0,
        VOICE_PLAYER_PLAYING,
        VOICE_PLAYER_ERROR
    } voice_player_status_t;

    /**
     * @brief Initialize the voice player
     *
     * @param audio_instance Pointer to the global Audio instance
     * @return true on success, false on failure
     */
    bool voice_player_init(Audio *audio_instance);

    /**
     * @brief Deinitialize the voice player
     */
    void voice_player_deinit(void);

    /**
     * @brief Play voice data from memory
     *
     * @param voice_data Pointer to voice data in memory
     * @param data_size Size of voice data in bytes
     * @param filename Filename for temporary file (without path)
     * @return true on success, false on failure
     */
    bool voice_player_play_from_memory(const uint8_t *voice_data,
                                       size_t data_size,
                                       const char *filename);

    /**
     * @brief Stop current voice playback
     *
     * @return true on success, false on failure
     */
    bool voice_player_stop(void);

    /**
     * @brief Check if voice player is currently playing
     *
     * @return true if playing, false otherwise
     */
    bool voice_player_is_playing(void);

    /**
     * @brief Get current voice player status
     *
     * @return voice_player_status_t Current status
     */
    voice_player_status_t voice_player_get_status(void);

    /**
     * @brief Process voice player (call in main loop)
     *
     * This function should be called regularly to handle playback completion
     * and cleanup temporary files.
     */
    void voice_player_process(void);

    /**
     * @brief Set voice volume
     *
     * @param volume Volume level (0-21)
     */
    void voice_player_set_volume(uint8_t volume);

    /**
     * @brief Get current volume
     *
     * @return uint8_t Current volume level
     */
    uint8_t voice_player_get_volume(void);

#ifdef __cplusplus
}
#endif

#endif // VOICE_PLAYER_H
