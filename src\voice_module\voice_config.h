#ifndef VOICE_CONFIG_H
#define VOICE_CONFIG_H

/**
 * Voice Module Configuration
 * 语音模块配置文件
 *
 * 此文件包含语音模块的所有配置选项，可根据项目需求进行调整
 */

#ifdef __cplusplus
extern "C"
{
#endif

// =============================================================================
// 基本配置
// =============================================================================

/** 语音文件数量 */
#define VOICE_FILE_COUNT 10

/** 最大文件名长度 */
#define VOICE_MAX_FILENAME_LENGTH 32

/** 临时文件路径前缀 */
#define VOICE_TEMP_FILE_PREFIX "/tmp_voice_"

// =============================================================================
// 内存管理配置
// =============================================================================

/** 是否启用内存管理模块 */
#define VOICE_ENABLE_MEMORY_MANAGEMENT 0

/** 最大内存缓冲区数量 */
#define VOICE_MAX_MEMORY_BUFFERS 5

/** 最大内存使用量 (字节) */
#define VOICE_MAX_MEMORY_SIZE (512 * 1024)

/** 是否优先使用PSRAM */
#define VOICE_PREFER_PSRAM 1

// =============================================================================
// 播放器配置
// =============================================================================

/** 默认音量 (0-21) */
#define VOICE_DEFAULT_VOLUME 21

/** 播放状态检查间隔 (毫秒) */
#define VOICE_PROCESS_INTERVAL_MS 10

/** 临时文件清理延迟 (毫秒) */
#define VOICE_CLEANUP_DELAY_MS 100

// =============================================================================
// 错误处理配置
// =============================================================================

/** 是否启用详细错误日志 */
#define VOICE_ENABLE_VERBOSE_LOGGING 1

/** 是否启用性能统计 */
#define VOICE_ENABLE_PERFORMANCE_STATS 0

/** 最大重试次数 */
#define VOICE_MAX_RETRY_COUNT 3

// =============================================================================
// 文件验证配置
// =============================================================================

/** 是否启用WAV文件头验证 */
#define VOICE_ENABLE_WAV_VALIDATION 1

/** 最小有效文件大小 (字节) */
#define VOICE_MIN_FILE_SIZE 44

/** 最大有效文件大小 (字节) */
#define VOICE_MAX_FILE_SIZE (1024 * 1024)

// =============================================================================
// 调试配置
// =============================================================================

/** 是否启用调试模式 */
#ifndef VOICE_DEBUG_MODE
#define VOICE_DEBUG_MODE 0
#endif

/** 调试日志级别 */
#define VOICE_DEBUG_LEVEL_ERROR 1
#define VOICE_DEBUG_LEVEL_WARNING 2
#define VOICE_DEBUG_LEVEL_INFO 3
#define VOICE_DEBUG_LEVEL_DEBUG 4

#ifndef VOICE_DEBUG_LEVEL
#define VOICE_DEBUG_LEVEL VOICE_DEBUG_LEVEL_INFO
#endif

// =============================================================================
// 平台特定配置
// =============================================================================

/** 是否为ESP32平台 */
#ifndef VOICE_PLATFORM_ESP32
#define VOICE_PLATFORM_ESP32 1
#endif

/** 是否支持LittleFS */
#ifndef VOICE_SUPPORT_LITTLEFS
#define VOICE_SUPPORT_LITTLEFS 1
#endif

/** 是否支持SPIFFS */
#ifndef VOICE_SUPPORT_SPIFFS
#define VOICE_SUPPORT_SPIFFS 0
#endif

// =============================================================================
// 性能优化配置
// =============================================================================

/** 是否启用快速启动模式 */
#define VOICE_ENABLE_FAST_BOOT 1

/** 是否启用文件预加载 */
#define VOICE_ENABLE_PRELOAD 0

/** 预加载文件索引列表 */
#define VOICE_PRELOAD_FILES {0, 1, 8} // 常用文件

/** 是否启用播放队列 */
#define VOICE_ENABLE_PLAY_QUEUE 0

/** 播放队列最大长度 */
#define VOICE_PLAY_QUEUE_SIZE 5

// =============================================================================
// 兼容性配置
// =============================================================================

/** 是否保持与旧API的兼容性 */
#define VOICE_ENABLE_LEGACY_API 1

/** 是否启用线程安全 */
#define VOICE_ENABLE_THREAD_SAFE 0

/** 是否启用中断安全 */
#define VOICE_ENABLE_INTERRUPT_SAFE 0

    // =============================================================================
    // 编译时检查
    // =============================================================================

#if VOICE_FILE_COUNT > 255
#error "VOICE_FILE_COUNT cannot exceed 255"
#endif

#if VOICE_DEFAULT_VOLUME > 21
#error "VOICE_DEFAULT_VOLUME cannot exceed 21"
#endif

#if VOICE_MAX_MEMORY_SIZE < (64 * 1024)
#warning "VOICE_MAX_MEMORY_SIZE is very small, may cause issues"
#endif

    // =============================================================================
    // 日志宏定义
    // =============================================================================

#if VOICE_ENABLE_VERBOSE_LOGGING

#define VOICE_LOG_E(format, ...) log_e("[VOICE] " format, ##__VA_ARGS__)
#define VOICE_LOG_W(format, ...) log_w("[VOICE] " format, ##__VA_ARGS__)
#define VOICE_LOG_I(format, ...) log_i("[VOICE] " format, ##__VA_ARGS__)
#define VOICE_LOG_D(format, ...) log_d("[VOICE] " format, ##__VA_ARGS__)

#else

#define VOICE_LOG_E(format, ...)
#define VOICE_LOG_W(format, ...)
#define VOICE_LOG_I(format, ...)
#define VOICE_LOG_D(format, ...)

#endif

    // =============================================================================
    // 性能统计宏
    // =============================================================================

#if VOICE_ENABLE_PERFORMANCE_STATS

#define VOICE_PERF_START(name) unsigned long _perf_##name = millis()
#define VOICE_PERF_END(name) VOICE_LOG_D("PERF: %s took %lu ms", #name, millis() - _perf_##name)

#else

#define VOICE_PERF_START(name)
#define VOICE_PERF_END(name)

#endif

// =============================================================================
// 内存对齐宏
// =============================================================================

/** 内存对齐大小 */
#define VOICE_MEMORY_ALIGNMENT 4

/** 内存对齐宏 */
#define VOICE_ALIGN_SIZE(size) (((size) + VOICE_MEMORY_ALIGNMENT - 1) & ~(VOICE_MEMORY_ALIGNMENT - 1))

    // =============================================================================
    // 版本信息
    // =============================================================================

#define VOICE_MODULE_VERSION_MAJOR 1
#define VOICE_MODULE_VERSION_MINOR 0
#define VOICE_MODULE_VERSION_PATCH 0

#define VOICE_MODULE_VERSION_STRING "1.0.0"

#ifdef __cplusplus
}
#endif

#endif // VOICE_CONFIG_H
