# This is a basic workflow to help you get started with Actions for Arduino library projects

name: Arduino Library Checks

# Controls when the action will run.
on:
  # Triggers the workflow on push or pull request events but only for the develop branch
  push:
    branches: [develop,master]
  pull_request:
    branches: [develop,master]

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

jobs:

  # This defines a job for checking the Arduino library format specifications
  # see <https://github.com/marketplace/actions/arduino-arduino-lint-action>
  lint:
    name: check library format
    runs-on: ubuntu-latest
    continue-on-error: true

    steps:
      - uses: actions/checkout@v3

      # Arduino - lint
      - name: Arduino-lint
        uses: arduino/arduino-lint-action@v1
        with:
          library-manager: update
          verbose: false

  # These jobs are used to compile the examples fot the specific processor/board.
  # see <https://github.com/marketplace/actions/compile-arduino-sketches>
  compile-uno:
    name: compile uno examples
    runs-on: ubuntu-latest
    continue-on-error: true

    steps:
      - uses: actions/checkout@v3

      # Compile Examples for UNO
      - name: Compile examples on uno
        uses: arduino/compile-sketches@v1
        with:
          verbose: true
          fqbn: arduino:avr:uno
          sketch-paths: |
            - 'examples/SimpleOneButton'
            - 'examples/TwoButtons'
            - 'examples/BlinkMachine'
            - 'examples/InterruptOneButton'
            - 'examples/SpecialInput'

  compile-esp8266:
    name: compile esp8266 examples
    runs-on: ubuntu-latest
    continue-on-error: true

    steps:
      - uses: actions/checkout@v3
      - name: compile sketches
        uses: arduino/compile-sketches@v1
        with:
          verbose: true
          platforms: |
            # Install ESP8266 platform via Boards Manager
            - name: esp8266:esp8266
              source-url: https://arduino.esp8266.com/stable/package_esp8266com_index.json
              version: 3.1.2
          fqbn: esp8266:esp8266:nodemcuv2
          sketch-paths: |
            - 'examples/SimpleOneButton'
            - 'examples/TwoButtons'
            - 'examples/BlinkMachine'
            - 'examples/InterruptOneButton'
            - 'examples/SpecialInput'

  compile-esp32:
    name: build examples on ESP32
    runs-on: ubuntu-latest
    continue-on-error: true

    steps:
      - uses: actions/checkout@v3
      - name: compile sketches
        uses: arduino/compile-sketches@v1
        with:
          verbose: true
          platforms: |
            # Install ESP32 platform via Boards Manager
            - name: "esp32:esp32"
              source-url: https://raw.githubusercontent.com/espressif/arduino-esp32/gh-pages/package_esp32_index.json
              # version: 2.0.4 doesn't work with CLI caused by issue https://github.com/espressif/arduino-esp32/pull/7060
              version: 2.0.8
          fqbn: esp32:esp32:esp32
          sketch-paths: |
            - 'examples/SimpleOneButton'
            - 'examples/TwoButtons'
            - 'examples/BlinkMachine'
            - 'examples/InterruptOneButton'
            - 'examples/SpecialInput'
