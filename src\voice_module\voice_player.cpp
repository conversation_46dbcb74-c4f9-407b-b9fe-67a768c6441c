#include "voice_player.h"
#include <FS.h>
#include <LittleFS.h>

// Voice player state
static Audio *g_audio = nullptr;
static voice_player_status_t g_status = VOICE_PLAYER_IDLE;
static String g_current_temp_file = "";
static uint8_t g_volume = 21;
static bool g_initialized = false;

// Forward declarations for static functions
static void voice_player_cleanup_temp_file(void);
static bool voice_player_create_temp_file(const uint8_t *voice_data,
                                          size_t data_size,
                                          const char *filename);

bool voice_player_init(Audio *audio_instance)
{
    if (audio_instance == nullptr)
    {
        log_e("Audio instance is null");
        return false;
    }

    if (g_initialized)
    {
        log_w("Voice player already initialized");
        return true;
    }

    g_audio = audio_instance;
    g_status = VOICE_PLAYER_IDLE;
    g_current_temp_file = "";
    g_initialized = true;

    log_i("Voice player initialized");
    return true;
}

void voice_player_deinit(void)
{
    if (!g_initialized)
    {
        return;
    }

    // Stop any ongoing playback
    voice_player_stop();

    // Clean up
    voice_player_cleanup_temp_file();

    g_audio = nullptr;
    g_status = VOICE_PLAYER_IDLE;
    g_current_temp_file = "";
    g_initialized = false;

    log_i("Voice player deinitialized");
}

bool voice_player_play_from_memory(const uint8_t *voice_data,
                                   size_t data_size,
                                   const char *filename)
{
    if (!g_initialized || g_audio == nullptr)
    {
        log_e("Voice player not initialized");
        return false;
    }

    if (voice_data == nullptr || data_size == 0 || filename == nullptr)
    {
        log_e("Invalid parameters");
        return false;
    }

    if (g_status == VOICE_PLAYER_PLAYING)
    {
        log_w("Voice player is busy");
        return false;
    }

    // Clean up any previous temporary file
    voice_player_cleanup_temp_file();

    // Create temporary file from memory data
    if (!voice_player_create_temp_file(voice_data, data_size, filename))
    {
        log_e("Failed to create temporary file");
        return false;
    }

    // Start playback
    if (!g_audio->connecttoFS(LittleFS, g_current_temp_file.c_str()))
    {
        log_e("Failed to connect to voice file: %s", g_current_temp_file.c_str());
        voice_player_cleanup_temp_file();
        g_status = VOICE_PLAYER_ERROR;
        return false;
    }

    g_status = VOICE_PLAYER_PLAYING;
    log_i("Started playing voice: %s", filename);

    return true;
}

bool voice_player_stop(void)
{
    if (!g_initialized || g_audio == nullptr)
    {
        return false;
    }

    if (g_status == VOICE_PLAYER_PLAYING && g_audio->isRunning())
    {
        g_audio->stopSong();
        log_i("Stopped voice playback");
    }

    voice_player_cleanup_temp_file();
    g_status = VOICE_PLAYER_IDLE;

    return true;
}

bool voice_player_is_playing(void)
{
    if (!g_initialized || g_audio == nullptr)
    {
        return false;
    }

    return g_status == VOICE_PLAYER_PLAYING && g_audio->isRunning();
}

voice_player_status_t voice_player_get_status(void)
{
    return g_status;
}

void voice_player_process(void)
{
    if (!g_initialized || g_audio == nullptr)
    {
        return;
    }

    // Check if playback has finished
    if (g_status == VOICE_PLAYER_PLAYING && !g_audio->isRunning())
    {
        log_d("Voice playback completed");
        voice_player_cleanup_temp_file();
        g_status = VOICE_PLAYER_IDLE;
    }
}

void voice_player_set_volume(uint8_t volume)
{
    if (volume > 21)
    {
        volume = 21;
    }

    g_volume = volume;

    if (g_initialized && g_audio != nullptr)
    {
        g_audio->setVolume(volume);
    }
}

uint8_t voice_player_get_volume(void)
{
    return g_volume;
}

// Static function implementations
static void voice_player_cleanup_temp_file(void)
{
    if (g_current_temp_file.length() > 0 && LittleFS.exists(g_current_temp_file))
    {
        LittleFS.remove(g_current_temp_file);
        log_d("Cleaned up temporary file: %s", g_current_temp_file.c_str());
    }
    g_current_temp_file = "";
}

static bool voice_player_create_temp_file(const uint8_t *voice_data,
                                          size_t data_size,
                                          const char *filename)
{
    // Create temporary file path
    g_current_temp_file = "/tmp_" + String(filename) + ".wav";

    // Write voice data to temporary file
    File temp_file = LittleFS.open(g_current_temp_file, "w");
    if (!temp_file)
    {
        log_e("Failed to create temporary file: %s", g_current_temp_file.c_str());
        g_current_temp_file = "";
        return false;
    }

    size_t written = temp_file.write(voice_data, data_size);
    temp_file.close();

    if (written != data_size)
    {
        LittleFS.remove(g_current_temp_file);
        g_current_temp_file = "";
        log_e("Failed to write complete voice data to temporary file");
        return false;
    }

    log_d("Created temporary voice file: %s (%zu bytes)",
          g_current_temp_file.c_str(), written);

    return true;
}
