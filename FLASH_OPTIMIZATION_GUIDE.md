# Flash空间优化指南

## 🚨 问题描述
```
Error: The program size (4636765 bytes) is greater than maximum allowed (3276800 bytes)
Flash: [==========]  141.5% (used 4636765 bytes from 3276800 bytes)
```

程序大小超出了Flash分区限制，主要原因是嵌入了约2.14MB的语音文件。

## ✅ 解决方案

### 方案1: 使用更大的分区表（推荐）

#### 1.1 新的分区表配置
已创建 `ble_gateway_partition_large.csv`，将应用分区从3.2MB增加到5MB：

```csv
# Name,   Type, SubType, Offset,  Size, Flags
nvs,      data, nvs,     0x9000,  0x6000,
otadata,  data, ota,     0xf000,  0x2000,
app0,     app,  ota_0,   0x11000, 0x500000,  # 5MB (原来3.2MB)
app1,     app,  ota_1,   0x511000,0x500000,  # 5MB (原来3.2MB)
spiffs,   data, spiffs,  0xA11000,0x5E0000,  # 约6MB
coredump, data, coredump,0xFF1000,0xF000,
```

#### 1.2 修改platformio.ini
```ini
board_build.arduino.partitions = ble_gateway_partition_large.csv
```

### 方案2: 最小语音模式（测试用）

#### 2.1 使用最小配置
创建了 `platformio_minimal_voice.ini`，只包含3个最重要的语音文件：
- network_success.wav (168KB)
- new_message.wav (111KB)  
- open_app_to_config.wav (279KB)

总计约558KB，大幅减少Flash使用。

#### 2.2 切换到最小模式
```bash
# 重命名配置文件
mv platformio.ini platformio_full.ini
mv platformio_minimal_voice.ini platformio.ini
```

### 方案3: 编译优化

#### 3.1 已应用的优化
```ini
build_flags = 
    -Os                    # 优化代码大小
    -ffunction-sections    # 分离函数段
    -fdata-sections        # 分离数据段
    -Wl,--gc-sections      # 链接时垃圾回收
    -D CORE_DEBUG_LEVEL=ARDUHAL_LOG_LEVEL_INFO  # 减少调试信息
    -D CONFIG_MBEDTLS_SSL_IN_CONTENT_LEN=4096   # 减少SSL缓冲区
    -D CONFIG_MBEDTLS_SSL_OUT_CONTENT_LEN=4096  # 减少SSL缓冲区
```

## 🔧 实施步骤

### 步骤1: 清理构建缓存
```bash
# 在VSCode中
Ctrl+Shift+P -> "PlatformIO: Clean"
```

### 步骤2: 选择构建方案

#### 选项A: 完整语音功能（推荐）
```bash
# 使用大分区表，支持所有语音文件
# platformio.ini 已配置为使用 ble_gateway_partition_large.csv
```

#### 选项B: 最小语音功能（测试用）
```bash
# 切换到最小配置
ren platformio.ini platformio_full.ini
ren platformio_minimal_voice.ini platformio.ini
```

### 步骤3: 重新构建
```bash
# 在VSCode中
Ctrl+Shift+P -> "PlatformIO: Build"
```

## 📊 空间分析

### 语音文件大小分析
```
open_app_to_config.wav    278,862 bytes  (最大)
tap_smart_config.wav      366,414 bytes  (最大)
blood_glucose_data.wav    221,262 bytes
blood_oxygen_data.wav     218,958 bytes
blood_pressure_data.wav   205,134 bytes
temperature_data.wav      198,222 bytes
select_user.wav           193,614 bytes
weight_data.wav           182,094 bytes
network_success.wav       168,270 bytes
new_message.wav           110,670 bytes  (最小)
-------------------------------------------
总计:                   2,143,500 bytes (~2.14MB)
```

### 分区空间对比
```
原分区表:
- app0: 3.2MB
- app1: 3.2MB
- spiffs: 9.6MB

新分区表:
- app0: 5.0MB (+1.8MB)
- app1: 5.0MB (+1.8MB)  
- spiffs: 6.0MB (-3.6MB)
```

## 🎯 推荐配置

### 生产环境（完整功能）
```ini
[env:esp32-s3-devkitc-1-N16R8]
board_build.arduino.partitions = ble_gateway_partition_large.csv
# 包含所有10个语音文件
```

### 开发测试（快速迭代）
```ini
[env:esp32-s3-devkitc-1-N16R8-minimal]
board_build.arduino.partitions = ble_gateway_partition_large.csv
build_flags = -DVOICE_MINIMAL_MODE=1
# 只包含3个核心语音文件
```

## 🔍 进一步优化建议

### 1. 语音文件压缩
- 降低采样率：44.1kHz → 22.05kHz 或 16kHz
- 减少位深度：16-bit → 8-bit
- 转换为单声道
- 使用音频压缩工具

### 2. 动态加载
- 将部分语音文件存储在SPIFFS中
- 按需从网络下载非关键语音文件
- 实现语音文件缓存机制

### 3. 代码优化
- 移除未使用的库和功能
- 优化LVGL配置
- 减少静态缓冲区大小

## 🚀 验证成功

构建成功后应该看到：
```
Flash: [====      ]  85.2% (used 4267890 bytes from 5242880 bytes)
RAM:   [==        ]  24.0% (used 78636 bytes from 327680 bytes)
```

## 📞 故障排除

### 如果仍然超出空间限制：
1. 检查分区表是否正确应用
2. 确认使用了优化的编译选项
3. 考虑使用最小语音模式
4. 检查是否有其他大文件被意外包含

### 如果OTA更新失败：
1. 确保两个app分区大小相同
2. 检查OTA分区配置
3. 验证固件大小在分区限制内
