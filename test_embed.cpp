#include <Arduino.h>

// Test embedded voice file access
extern const uint8_t voice_network_success_wav_start[] asm("_binary_voice_files_network_success_wav_start");
extern const uint8_t voice_network_success_wav_end[] asm("_binary_voice_files_network_success_wav_end");

void setup() {
    Serial.begin(115200);
    delay(2000);
    
    Serial.println("Testing embedded voice file access...");
    
    // Calculate size
    size_t size = voice_network_success_wav_end - voice_network_success_wav_start;
    
    Serial.printf("Voice file size: %zu bytes\n", size);
    Serial.printf("Start address: %p\n", voice_network_success_wav_start);
    Serial.printf("End address: %p\n", voice_network_success_wav_end);
    
    if (size > 0 && size < 1000000) {
        Serial.println("✅ Voice file embedded successfully!");
        
        // Check WAV header
        if (size >= 12) {
            if (voice_network_success_wav_start[0] == 'R' && 
                voice_network_success_wav_start[1] == 'I' && 
                voice_network_success_wav_start[2] == 'F' && 
                voice_network_success_wav_start[3] == 'F') {
                Serial.println("✅ WAV header detected!");
            } else {
                Serial.println("⚠️ WAV header not found");
            }
        }
    } else {
        Serial.println("❌ Voice file embedding failed!");
    }
}

void loop() {
    delay(1000);
}
