[env:test_embed]
platform = espressif32
board = esp32-s3-devkitc-1
framework = arduino

; Test configuration for voice file embedding
build_flags = 
    -DCORE_DEBUG_LEVEL=4
    -DBOARD_HAS_PSRAM
    -DARDUINO_USB_CDC_ON_BOOT=1

; Embed only one voice file for testing
board_build.embed_files = 
    voice_files/network_success.wav

; Memory settings
board_build.partitions = huge_app.csv
board_build.arduino.memory_type = qio_opi

; Upload and monitor settings
upload_speed = 921600
monitor_speed = 115200
monitor_filters = esp32_exception_decoder
